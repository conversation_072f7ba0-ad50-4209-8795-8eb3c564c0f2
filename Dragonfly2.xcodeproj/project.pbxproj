// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		4F213A2E2DE0909000D5B4B2 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 4F213A182DE0908D00D5B4B2 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 4F213A1F2DE0908D00D5B4B2;
			remoteInfo = Dragonfly2;
		};
		4F213A382DE0909000D5B4B2 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 4F213A182DE0908D00D5B4B2 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 4F213A1F2DE0908D00D5B4B2;
			remoteInfo = Dragonfly2;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		4F213A202DE0908D00D5B4B2 /* Dragonfly2.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Dragonfly2.app; sourceTree = BUILT_PRODUCTS_DIR; };
		4F213A2D2DE0909000D5B4B2 /* Dragonfly2Tests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = Dragonfly2Tests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		4F213A372DE0909000D5B4B2 /* Dragonfly2UITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = Dragonfly2UITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		4F213A222DE0908D00D5B4B2 /* Dragonfly2 */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = Dragonfly2;
			sourceTree = "<group>";
		};
		4F213A302DE0909000D5B4B2 /* Dragonfly2Tests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = Dragonfly2Tests;
			sourceTree = "<group>";
		};
		4F213A3A2DE0909000D5B4B2 /* Dragonfly2UITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = Dragonfly2UITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		4F213A1D2DE0908D00D5B4B2 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4F213A2A2DE0909000D5B4B2 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4F213A342DE0909000D5B4B2 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		4F213A172DE0908D00D5B4B2 = {
			isa = PBXGroup;
			children = (
				4F213A222DE0908D00D5B4B2 /* Dragonfly2 */,
				4F213A302DE0909000D5B4B2 /* Dragonfly2Tests */,
				4F213A3A2DE0909000D5B4B2 /* Dragonfly2UITests */,
				4F213A212DE0908D00D5B4B2 /* Products */,
			);
			sourceTree = "<group>";
		};
		4F213A212DE0908D00D5B4B2 /* Products */ = {
			isa = PBXGroup;
			children = (
				4F213A202DE0908D00D5B4B2 /* Dragonfly2.app */,
				4F213A2D2DE0909000D5B4B2 /* Dragonfly2Tests.xctest */,
				4F213A372DE0909000D5B4B2 /* Dragonfly2UITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		4F213A1F2DE0908D00D5B4B2 /* Dragonfly2 */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 4F213A412DE0909000D5B4B2 /* Build configuration list for PBXNativeTarget "Dragonfly2" */;
			buildPhases = (
				4F213A1C2DE0908D00D5B4B2 /* Sources */,
				4F213A1D2DE0908D00D5B4B2 /* Frameworks */,
				4F213A1E2DE0908D00D5B4B2 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				4F213A222DE0908D00D5B4B2 /* Dragonfly2 */,
			);
			name = Dragonfly2;
			packageProductDependencies = (
			);
			productName = Dragonfly2;
			productReference = 4F213A202DE0908D00D5B4B2 /* Dragonfly2.app */;
			productType = "com.apple.product-type.application";
		};
		4F213A2C2DE0909000D5B4B2 /* Dragonfly2Tests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 4F213A442DE0909000D5B4B2 /* Build configuration list for PBXNativeTarget "Dragonfly2Tests" */;
			buildPhases = (
				4F213A292DE0909000D5B4B2 /* Sources */,
				4F213A2A2DE0909000D5B4B2 /* Frameworks */,
				4F213A2B2DE0909000D5B4B2 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				4F213A2F2DE0909000D5B4B2 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				4F213A302DE0909000D5B4B2 /* Dragonfly2Tests */,
			);
			name = Dragonfly2Tests;
			packageProductDependencies = (
			);
			productName = Dragonfly2Tests;
			productReference = 4F213A2D2DE0909000D5B4B2 /* Dragonfly2Tests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		4F213A362DE0909000D5B4B2 /* Dragonfly2UITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 4F213A472DE0909000D5B4B2 /* Build configuration list for PBXNativeTarget "Dragonfly2UITests" */;
			buildPhases = (
				4F213A332DE0909000D5B4B2 /* Sources */,
				4F213A342DE0909000D5B4B2 /* Frameworks */,
				4F213A352DE0909000D5B4B2 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				4F213A392DE0909000D5B4B2 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				4F213A3A2DE0909000D5B4B2 /* Dragonfly2UITests */,
			);
			name = Dragonfly2UITests;
			packageProductDependencies = (
			);
			productName = Dragonfly2UITests;
			productReference = 4F213A372DE0909000D5B4B2 /* Dragonfly2UITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		4F213A182DE0908D00D5B4B2 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1630;
				LastUpgradeCheck = 1630;
				TargetAttributes = {
					4F213A1F2DE0908D00D5B4B2 = {
						CreatedOnToolsVersion = 16.3;
					};
					4F213A2C2DE0909000D5B4B2 = {
						CreatedOnToolsVersion = 16.3;
						TestTargetID = 4F213A1F2DE0908D00D5B4B2;
					};
					4F213A362DE0909000D5B4B2 = {
						CreatedOnToolsVersion = 16.3;
						TestTargetID = 4F213A1F2DE0908D00D5B4B2;
					};
				};
			};
			buildConfigurationList = 4F213A1B2DE0908D00D5B4B2 /* Build configuration list for PBXProject "Dragonfly2" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 4F213A172DE0908D00D5B4B2;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 4F213A212DE0908D00D5B4B2 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				4F213A1F2DE0908D00D5B4B2 /* Dragonfly2 */,
				4F213A2C2DE0909000D5B4B2 /* Dragonfly2Tests */,
				4F213A362DE0909000D5B4B2 /* Dragonfly2UITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		4F213A1E2DE0908D00D5B4B2 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4F213A2B2DE0909000D5B4B2 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4F213A352DE0909000D5B4B2 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		4F213A1C2DE0908D00D5B4B2 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4F213A292DE0909000D5B4B2 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4F213A332DE0909000D5B4B2 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		4F213A2F2DE0909000D5B4B2 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 4F213A1F2DE0908D00D5B4B2 /* Dragonfly2 */;
			targetProxy = 4F213A2E2DE0909000D5B4B2 /* PBXContainerItemProxy */;
		};
		4F213A392DE0909000D5B4B2 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 4F213A1F2DE0908D00D5B4B2 /* Dragonfly2 */;
			targetProxy = 4F213A382DE0909000D5B4B2 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		4F213A3F2DE0909000D5B4B2 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		4F213A402DE0909000D5B4B2 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		4F213A422DE0909000D5B4B2 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = C4VB65KK8Y;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = totalflow.com.ua.Dragonfly2;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		4F213A432DE0909000D5B4B2 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = C4VB65KK8Y;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = totalflow.com.ua.Dragonfly2;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		4F213A452DE0909000D5B4B2 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = totalflow.com.ua.Dragonfly2Tests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Dragonfly2.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Dragonfly2";
			};
			name = Debug;
		};
		4F213A462DE0909000D5B4B2 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = totalflow.com.ua.Dragonfly2Tests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Dragonfly2.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Dragonfly2";
			};
			name = Release;
		};
		4F213A482DE0909000D5B4B2 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = totalflow.com.ua.Dragonfly2UITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = Dragonfly2;
			};
			name = Debug;
		};
		4F213A492DE0909000D5B4B2 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = totalflow.com.ua.Dragonfly2UITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = Dragonfly2;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		4F213A1B2DE0908D00D5B4B2 /* Build configuration list for PBXProject "Dragonfly2" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				4F213A3F2DE0909000D5B4B2 /* Debug */,
				4F213A402DE0909000D5B4B2 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		4F213A412DE0909000D5B4B2 /* Build configuration list for PBXNativeTarget "Dragonfly2" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				4F213A422DE0909000D5B4B2 /* Debug */,
				4F213A432DE0909000D5B4B2 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		4F213A442DE0909000D5B4B2 /* Build configuration list for PBXNativeTarget "Dragonfly2Tests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				4F213A452DE0909000D5B4B2 /* Debug */,
				4F213A462DE0909000D5B4B2 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		4F213A472DE0909000D5B4B2 /* Build configuration list for PBXNativeTarget "Dragonfly2UITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				4F213A482DE0909000D5B4B2 /* Debug */,
				4F213A492DE0909000D5B4B2 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 4F213A182DE0908D00D5B4B2 /* Project object */;
}
